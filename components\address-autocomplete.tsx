import { useRef, useState } from "react"
import { Input } from "@/components/ui/input"

interface AddressAutocompleteProps {
  onAddressSelect: (address: {
    street: string
    city: string
    province: string
    postalCode: string
  }) => void
  value: string
  onChange: (value: string) => void
}

interface AddressComponent {
  long_name: string
  short_name: string
  types: string[]
}

interface PlaceSuggestion {
  place_id: string
  description: string
}

export default function AddressAutocomplete({
  onAddressSelect,
  value,
  onChange
}: AddressAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [suggestions, setSuggestions] = useState<PlaceSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  const fetchPlaces = async (query: string) => {
    if (!query) {
      setSuggestions([])
      return
    }

    try {
      const response = await fetch(`/api/places?query=${encodeURIComponent(query)}`)
      const data = await response.json()
      setSuggestions(data.predictions || [])
    } catch (error) {
      console.error('Error fetching places:', error)
      setSuggestions([])
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    onChange(value)
    fetchPlaces(value)
    setShowSuggestions(true)
  }

  const handleSuggestionClick = async (placeId: string) => {
    try {
      const response = await fetch(`/api/places/details?placeId=${placeId}`)
      const data = await response.json()
      
      if (data.result) {
        const addressComponents = data.result.address_components as AddressComponent[]
        const street = addressComponents.find((component) => 
          component.types.includes("route")
        )?.long_name || ""
        const streetNumber = addressComponents.find((component) => 
          component.types.includes("street_number")
        )?.long_name || ""
        const city = addressComponents.find((component) => 
          component.types.includes("locality")
        )?.long_name || ""
        const province = addressComponents.find((component) => 
          component.types.includes("administrative_area_level_1")
        )?.short_name || ""
        const postalCode = addressComponents.find((component) => 
          component.types.includes("postal_code")
        )?.long_name || ""

        onAddressSelect({
          street: `${streetNumber} ${street}`.trim(),
          city,
          province,
          postalCode
        })
        onChange(`${streetNumber} ${street}`.trim())
        setShowSuggestions(false)
      }
    } catch (error) {
      console.error('Error fetching place details:', error)
    }
  }

  return (
    <div className="relative">
      <Input
        ref={inputRef}
        type="text"
        placeholder="Enter your address"
        value={value}
        onChange={handleInputChange}
        className="w-full"
      />
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
          {suggestions.map((suggestion) => (
            <div
              key={suggestion.place_id}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSuggestionClick(suggestion.place_id)}
            >
              {suggestion.description}
            </div>
          ))}
        </div>
      )}
    </div>
  )
} 