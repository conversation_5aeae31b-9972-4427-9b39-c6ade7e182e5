import { NextResponse } from "next/server";

const MAPBOX_API_KEY = process.env.MAPBOX_ACCESS_TOKEN;

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get("query");

  if (!query) {
    return NextResponse.json({ error: "Query parameter is required" }, { status: 400 });
  }

  if (!MAPBOX_API_KEY) {
    console.error("Mapbox API key is not configured");
    return NextResponse.json({ error: "Mapbox API key is not configured" }, { status: 500 });
  }

  try {
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
        query
      )}.json?country=CA&types=address&access_token=${MAPBOX_API_KEY}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mapbox API error:", errorData);
      throw new Error(`Mapbox API error: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json({ data });
  } catch (error) {
    console.error("Error fetching places:", error);
    return NextResponse.json(
      { error: "Failed to fetch places" },
      { status: 500 }
    );
  }
} 