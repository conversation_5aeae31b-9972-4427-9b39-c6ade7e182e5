import bcrypt from "bcryptjs"
import { UserType } from "./auth"
import { getPool } from "./db"

export async function saltAndHashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10)
  return bcrypt.hash(password, salt)
}

export async function getUserFromDb(email: string, password: string) {
  const pool = getPool()
  const client = await pool.connect()
  
  try {
    const result = await client.query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    )
    
    if (result.rows.length > 0) {
      const user = result.rows[0]
      const isPasswordValid = await bcrypt.compare(password, user.password)
      
      if (isPasswordValid) {
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          userType: user.user_type as UserType,
        }
      }
    }
    
    return null
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  } finally {
    client.release()
  }
} 