import { Pool } from "@neondatabase/serverless"
import bcrypt from "bcryptjs"

export async function saltAndHashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10)
  return bcrypt.hash(password, salt)
}

export async function getUserFromDb(email: string, password: string) {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL })
  
  try {
    const result = await pool.query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    )
    
    if (result.rows.length > 0) {
      const user = result.rows[0]
      const isPasswordValid = await bcrypt.compare(password, user.password)
      
      if (isPasswordValid) {
        return {
          id: user.id,
          name: user.name,
          email: user.email,
        }
      }
    }
    
    return null
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  } finally {
    await pool.end()
  }
} 