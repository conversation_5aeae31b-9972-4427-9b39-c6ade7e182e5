{"name": "print-gservetech", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"@auth/neon-adapter": "^1.9.0", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@react-google-maps/api": "^2.20.6", "@types/bcryptjs": "^2.4.6", "@types/google.maps": "^3.58.1", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formidable": "^3.5.4", "lucide-react": "^0.507.0", "next": "^15.3.1", "next-auth": "^5.0.0-beta.27", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "print-gservetech": "file:", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.56.2", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.11.30", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.3", "postcss": "^8.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}