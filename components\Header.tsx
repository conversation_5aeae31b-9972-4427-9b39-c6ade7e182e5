"use client"

import Link from "next/link"


import { useSession, signOut } from "next-auth/react"
import { ShoppingCart, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useCartStore } from "@/store/cart-store"
import Image from "next/image"

const Header = () => {
  const { data: session } = useSession()
  
  
  

  const cartItemsCount = useCartStore((state) => state.getItemsCount());

  

  const navItems = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products", hasDropdown: true },
  ]

  return (
    <header className="w-full">
      <div className="bg-blue-600 text-white py-2 text-center">
        <p className="text-sm font-medium">PrintCloud is a Proudly Canadian Company</p>
      </div>

      <div className="bg-gray-900 text-white py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Link href="/" className="relative w-40 h-12 flex items-center">
            <Image src="/images/logo.png" alt="PrintCloud Logo" width={160} height={48} className="object-contain" />
          </Link>

          <div className="flex space-x-4">
            {navItems.map((item) => (
              <Link key={item.name} href={item.path} className="text-sm font-medium px-4 py-2">
                {item.name}
              </Link>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <Link href="/cart" className="relative">
              <ShoppingCart className="w-5 h-5" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>

            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-white">
                    <User className="h-5 w-5 mr-1" />
                    <span>{session.user?.name?.split(" ")[0] || "User"}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/account">My Account</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/orders">My Orders</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => signOut()}>Sign Out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link href="/login">
                <Button variant="ghost" size="sm" className="text-white">
                  <User className="h-5 w-5 mr-1" /> Sign In
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
