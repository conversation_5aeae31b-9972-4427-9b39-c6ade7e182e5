import { PoolClient } from 'pg';
import { getPool } from './db';

/**
 * Checks if a column exists in a table
 */
export async function columnExists(client: PoolClient, tableName: string, columnName: string): Promise<boolean> {
  try {
    const result = await client.query(
      `SELECT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = $1 
        AND column_name = $2
      ) AS "exists"`,
      [tableName, columnName]
    );
    
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking if column ${columnName} exists in table ${tableName}:`, error);
    return false;
  }
}

/**
 * Gets a database connection from the pool
 */
export async function getDbClient(): Promise<{ client: PoolClient; releaseClient: () => void }> {
  const pool = getPool();
  const client = await pool.connect();
  return { 
    client, 
    releaseClient: () => client.release() 
  };
}

/**
 * Executes a database transaction with proper error handling
 */
export async function executeTransaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const { client, releaseClient } = await getDbClient();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Transaction error:', error);
    throw error;
  } finally {
    releaseClient();
  }
}

/**
 * Safely get user information, handling potential schema differences
 */
export async function getUserInfo(client: PoolClient, userId: string): Promise<{ 
  id: string; 
  name?: string; 
  email: string; 
  phone?: string;
}> {
  // Check which columns exist
  const hasNameColumn = await columnExists(client, 'users', 'name');
  const hasPhoneColumn = await columnExists(client, 'users', 'phone');
  
  // Build a dynamic query based on column existence
  const columns = ['id', 'email'];
  if (hasNameColumn) columns.push('name');
  if (hasPhoneColumn) columns.push('phone');
  
  const query = `SELECT ${columns.join(', ')} FROM users WHERE id = $1`;
  const result = await client.query(query, [userId]);
  
  if (result.rows.length === 0) {
    throw new Error('User not found');
  }
  
  return result.rows[0];
} 