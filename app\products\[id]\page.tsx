import { getPool } from "@/lib/db";
import ProductDetail from './productDetail';
import { notFound } from "next/navigation";

type DBProduct = {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  subcategory_id?: number;
  created_at?: string;
};

async function getProduct(id: string): Promise<DBProduct | null> {
  const pool = getPool();
  const client = await pool.connect();
  
  try {
    const result = await client.query(
      "SELECT * FROM product WHERE id = $1",
      [id]
    );
    return result.rows[0] || null;
  } catch (error) {
    console.error("Error fetching product:", error);
    return null;
  } finally {
    client.release();
  }
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  const title = product.title;
  const description = product.short_description;

  return {
    title,
    description,
  };
}

export default async function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="bg-white w-full py-8">
      <ProductDetail product={product} />
    </div>
  );
}

