"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowRight, Award, Clock, Printer, Truck } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

const featuredProducts = [
  {
    id: "1",
    name: "Business Cards",
    description: "Premium quality business cards with fast turnaround",
    price: 49.99,
    image: "/images/busines-card.png",
    slug: "business-cards",
    express: true
  },
  {
    id: "2",
    name: "Flyers & Brochures",
    description: "High-impact marketing materials for your business",
    price: 99.99,
    image: "/images/premium.jpg",
    slug: "flyers",
    express: true
  },
  {
    id: "3",
    name: "Posters & Signs",
    description: "Large format printing for maximum visibility",
    price: 149.99,
    image: "/images/board.jpg",
    slug: "posters",
    express: false
  },
  {
    id: "4",
    name: "Banners & Displays",
    description: "Professional banners and display materials",
    price: 199.99,
    image: "/images/sign.jpg",
    slug: "banners",
    express: false
  }
]

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-blue-600 text-white">
        <div className="container mx-auto px-8 py-12 md:py-24 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-8 md:mb-0">
            <h1
              className="text-4xl md:text-5xl font-bold mb-4 animate-slide-in-bottom"
              style={{ animationDelay: "0.1s" }}
            >
              Professional Printing Services for Your Business
            </h1>
            <p className="text-xl mb-6 animate-slide-in-bottom" style={{ animationDelay: "0.2s" }}>
              High-quality printing solutions with fast turnaround times and competitive pricing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 animate-slide-in-bottom" style={{ animationDelay: "0.3s" }}>
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                Shop Products
              </Button>
              <Button size="lg" variant="outline" className="text-blue-600 border-white hover:bg-blue-700">
                Get a Quote
              </Button>
            </div>
          </div>
          <div className="md:w-1/2 flex justify-center animate-slide-in-bottom" style={{ animationDelay: "0.4s" }}>
            <Image
              src="/images/hero.jpeg"
              alt="Printing Services"
              width={600}
              height={400}
              className="rounded-lg shadow-lg"
            />
          </div>
        </div>
      </section>

      {/* Express Printing Banner */}
      {/* <section className="bg-blue-500 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <Image
                src="/placeholder.svg?height=100&width=100"
                alt="2 Day Express"
                width={100}
                height={100}
                className="mr-4"
              />
              <div>
                <h2 className="text-2xl md:text-3xl font-bold">2 DAY EXPRESS</h2>
                <p className="text-lg">A collection of print products that can be printed fast!</p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <div>
                <p className="font-bold mb-1">Need Same Day - Call Us!</p>
                <p className="text-sm">416-464-4999</p>
              </div>
              <div className="delivery-timeline">
                <div className="delivery-day active">
                  <div>MON</div>
                  <div>1</div>
                </div>
                <div className="delivery-day active">
                  <div>TUES</div>
                  <div>2</div>
                </div>
                <div className="delivery-day active">
                  <div>WED</div>
                  <div>3</div>
                </div>
                <div className="delivery-day inactive">
                  <div>THR</div>
                  <div>4</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Featured Products */}
      <section className="py-12 px-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Featured Products</h2>
            <Link href="/products" className="text-blue-600 hover:text-blue-800 flex items-center">
              View All Products <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <Link href={`/products/${product.slug}`} key={product.id} className="group">
                <Card className="overflow-hidden printcloud-card-hover h-full">
                  <div className="relative aspect-square">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform group-hover:scale-105"
                    />
                    {product.express && <span className="absolute top-2 right-2 express-badge">Express</span>}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1">{product.name}</h3>
                    <p className="text-gray-500 text-sm mb-2">{product.description}</p>
                    <p className="font-bold text-blue-600">from ${product.price.toFixed(2)} CAD</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Services */}
      <section className="px-8 py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Our Printing Services</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="printcloud-card-hover">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Express Printing</h3>
                <p className="text-gray-600 mb-4">
                  Get your printing done in as little as 24 hours with our express printing service.
                </p>
                <Link href="/express-printing" className="text-blue-600 hover:text-blue-800 mt-auto flex items-center">
                  Learn More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>

            <Card className="printcloud-card-hover">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Printer className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Custom Printing</h3>
                <p className="text-gray-600 mb-4">
                  Personalized printing solutions tailored to your specific business needs.
                </p>
                <Link href="/custom-printing" className="text-blue-600 hover:text-blue-800 mt-auto flex items-center">
                  Learn More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>

            <Card className="printcloud-card-hover">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Truck className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Print Fulfillment</h3>
                <p className="text-gray-600 mb-4">
                  End-to-end print fulfillment services including storage, packaging, and shipping.
                </p>
                <Link href="/print-fulfillment" className="text-blue-600 hover:text-blue-800 mt-auto flex items-center">
                  Learn More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="px-8 py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose PrintCloud</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">Quality Guaranteed</h3>
              <p className="text-gray-300">
                We use premium materials and state-of-the-art printing technology to ensure the highest quality.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <Clock className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">Fast Turnaround</h3>
              <p className="text-gray-300">
                With our express printing options, get your orders completed in as little as 24-48 hours.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <Truck className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">Free Shipping</h3>
              <p className="text-gray-300">Enjoy free shipping on all orders over $149 across Canada.</p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Competitive Pricing</h3>
              <p className="text-gray-300">
                Get the best value for your money with our competitive pricing and bulk discounts.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="px-8 py-12 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Whether you need business cards, flyers, posters, or custom printing solutions, we&apos;re here to help.ve got you covered.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              Shop Now
            </Button>
            <Button size="lg" variant="outline" className="text-blue-600 border-white hover:bg-blue-700">
              Request a Quote
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
