import { redirect } from "next/navigation"
import { auth } from "@/lib/auth" // Assuming you have auth configuration
import { AdminDashboard } from "@/components/admin-dashboard"
import { UserDashboard } from "@/components/user-dashboard"

export default async function DashboardPage() {
  const session = await auth()

  if (!session?.user) {
    redirect("/login")
  }

  // Check user role from session or database
  const userRole = session.user.userType || "user" // Assuming role is stored in session

  if (userRole === "admin") {
    return <AdminDashboard />
  } else {
    return <UserDashboard />
  }
}
