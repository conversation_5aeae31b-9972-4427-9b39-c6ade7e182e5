import { NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { getPool } from "@/lib/db"

export async function GET(
  request: Request,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const session = await auth()

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { orderId } = await params
    
    const pool = getPool()
    const client = await pool.connect()

    try {
      await client.query("BEGIN")

      const orderResult = await client.query(
        `SELECT o.*, da.street, da.city, da.province, da.postal_code
         FROM orders o
         LEFT JOIN delivery_addresses da ON o.id = da.order_id
         WHERE o.id = $1 AND o.user_id = $2`,
        [orderId, session.user.id]
      )

      if (orderResult.rows.length === 0) {
        await client.query("ROLLBACK")
        return new NextResponse("Order not found", { status: 404 })
      }

      const order = orderResult.rows[0]

      const itemsResult = await client.query(
        `SELECT oi.*, p.image_url as current_image_url 
         FROM order_items oi
         LEFT JOIN product p ON CAST(oi.product_id AS INTEGER) = p.id
         WHERE oi.order_id = $1`,
        [orderId]
      )

      const orderDetails = {
        orderNumber: order.order_number,
        status: order.status,
        items: itemsResult.rows.map(item => ({
          id: item.id,
          name: item.product_name,
          quantity: item.quantity,
          price: item.price_at_order,
          image: item.current_image_url || item.product_image || "/placeholder.svg",
          customDesignId: "custom-design"
        })),
        subtotal: itemsResult.rows.reduce((sum, item) => sum + Number(item.item_subtotal), 0),
        deliveryFee: order.delivery_fee,
        promotionalDiscount: order.promotional_discount,
        totalAmount: order.total_amount,
        deliveryMethod: order.delivery_method,
        deliveryAddress: order.street ? {
          street: order.street,
          city: order.city,
          province: order.province,
          postalCode: order.postal_code
        } : undefined,
        specialInstructions: order.special_instructions,
        createdAt: order.created_at,
        estimatedDeliveryTime: order.delivery_method === "DELIVERY" 
          ? new Date(new Date(order.created_at).getTime() + 45 * 60000).toLocaleString()
          : undefined,
        customDesignImageUrl: order.custom_design_image_url || null
      }

      await client.query("COMMIT")

      return NextResponse.json(orderDetails)
    } catch (error) {
      await client.query("ROLLBACK")
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error("Error fetching order details:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
} 