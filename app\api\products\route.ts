import { getPool } from "@/lib/db";
import { NextResponse } from "next/server";
import NodeCache from "node-cache";

// Cache TTL: 1 day (in seconds)
const TTL = 86400;
const productCache = new NodeCache();

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const featured = searchParams.get("featured");
  const categoryId = searchParams.get("category");
  const sortBy = searchParams.get("sort");

  // Build cache key based on query parameters
  const cacheKey = `products-${featured || "all"}-${categoryId || "all"}-${sortBy || "default"}`;
  const cachedData = productCache.get(cacheKey);

  if (cachedData) {
    return NextResponse.json({ products: cachedData });
  }

  let query = `SELECT 
        p.id, 
        p.title,
        p.short_description,
        p.long_description,
        p.price, 
        p.promotional_price_percent,
        p.sales_price_percent,
        p.image_url,
        p.gallery_urls,
        p.created_at,
        p.subcategory_id, 
        p.is_featured,  
        c.id as category_id,
        c.name as category_name
      FROM product p
      LEFT JOIN subcategory s ON p.subcategory_id = s.id
      LEFT JOIN category c ON s.category_id = c.id`;
  
  const params: unknown[] = [];
  let paramCount = 1;
  
  // Where clauses
  const whereConditions = [];
  
  if (featured === "true") {
    whereConditions.push("p.is_featured = true");
  }
  
  if (categoryId) {
    whereConditions.push(`c.id = $${paramCount}`);
    params.push(categoryId);
    paramCount++;
  }
  
  if (whereConditions.length > 0) {
    query += " WHERE " + whereConditions.join(" AND ");
  }
  
  // Add ORDER BY based on sorting preference
  if (sortBy === "price_asc") {
    query += " ORDER BY p.price ASC";
  } else if (sortBy === "price_desc") {
    query += " ORDER BY p.price DESC";
  } else {
    query += " ORDER BY p.created_at DESC";
  }
  
  // Skip cache for filtered/sorted queries
  if (categoryId || sortBy || featured === "true") {
    try {
      const result = await getPool().query(query, params);
      
      return NextResponse.json(
        { products: result.rows },
        {
          headers: {
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          }
        }
      );
    } catch (error) {
      console.error("Fetch products error:", error);
      return NextResponse.json(
        { error: "Failed to fetch products" },
        { status: 500 }
      );
    }
  }

  try {
    const result = await getPool().query(query, params);
    productCache.set(cacheKey, result.rows, TTL);
    return NextResponse.json(
      { products: result.rows },
      {
        headers: {
          'Cache-Control': 'max-age=60, s-maxage=60', // 1 minute cache to balance freshness and performance
        }
      }
    );
  } catch (error) {
    console.error("Fetch products error:", error);
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}