import fs from 'fs';
import path from 'path';
import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { Product } from '../../../types';

// Handle file upload
async function saveFile(file: File, uploadDir: string): Promise<string> {
  // Create uploads directory if it doesn't exist
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);
  
  const fileName = `${uuidv4()}_${file.name.replace(/\s/g, '_')}`;
  const filePath = path.join(uploadDir, fileName);
  
  await writeFile(filePath, buffer);
  return fileName;
}

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    
    // Get fields
    const title = formData.get('title') as string;
    const shortDesc = formData.get('shortDesc') as string;
    const longDesc = formData.get('longDesc') as string;
    const price = formData.get('price') as string;
    
    // Handle main image
    const imageFile = formData.get('image') as File;
    let mainImage = 'no-image.png';
    
    if (imageFile && imageFile instanceof File) {
      mainImage = await saveFile(imageFile, uploadDir);
    }
    
    // Handle gallery images
    const galleryFiles = formData.getAll('galleryImages') as File[];
    const galleryImages: string[] = [];
    
    for (const file of galleryFiles) {
      if (file instanceof File) {
        const fileName = await saveFile(file, uploadDir);
        galleryImages.push(`/uploads/${fileName}`);
      }
    }
    
    const newProduct: Product = {
      id: Date.now(),
      title,
      shortDesc,
      longDesc,
      price,
      image: `/uploads/${mainImage}`,
      galleryImages,
    };
    
    // Path to your products.json
    const filePath = path.join(process.cwd(), 'data', 'products.json');
    let products: Product[] = [];
    
    // Ensure folder exists
    if (!fs.existsSync(path.dirname(filePath))) {
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
    }
    
    // Read existing products
    if (fs.existsSync(filePath)) {
      const fileData = fs.readFileSync(filePath, 'utf8');
      products = JSON.parse(fileData || '[]');
    }
    
    // Add the new product
    products.push(newProduct);
    
    // Write back to JSON file
    fs.writeFileSync(filePath, JSON.stringify(products, null, 2), 'utf8');
    
    return NextResponse.json({ success: true, product: newProduct }, { status: 200 });
  } catch (error) {
    console.error('Error adding product:', error);
    return NextResponse.json({ error: 'Failed to add product' }, { status: 500 });
  }
} 