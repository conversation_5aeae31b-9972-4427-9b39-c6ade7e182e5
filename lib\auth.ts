import PostgresAdapter from "@auth/pg-adapter";
import NextAuth from "next-auth";
import Apple from "next-auth/providers/apple";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google";
import { getUserFromDb } from "./auth-utils";
import { getPool } from "./db";
import { Adapter } from "next-auth/adapters";

export type UserType = "user" | "admin";

declare module "next-auth" {
  interface User {
    userType: UserType;
  }

  interface Session {
    user: {
      role:string
      id: string;
      name: string;
      email: string;
      userType: UserType;
    };
  }
}

// Get the pool once for the adapter
const pool = getPool();

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PostgresAdapter(pool) as Adapter,
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === "google") {
        const client = await pool.connect();
        try {
          const result = await client.query(
            "SELECT * FROM users WHERE email = $1",
            [user.email]
          );
          if (result.rows.length > 0) {
            const existingUser = result.rows[0];
            user.id = existingUser.id;
            user.userType = existingUser.user_type;
          }
        } catch (error) {
          console.error("Error in signIn callback:", error);
          return false;
        } finally {
          client.release();
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.name = user.name as string;
        token.email = user.email as string;
      }
      if (token.email) {
        const client = await pool.connect();
        try {
          const result = await client.query(
            "SELECT user_type FROM users WHERE email = $1",
            [token.email as string]
          );
          if (result.rows.length > 0) {
            token.userType = result.rows[0].user_type as UserType;
          }
        } finally {
          client.release();
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.userType = token.userType as UserType;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password required");
        }

        const user = await getUserFromDb(
          credentials.email as string,
          credentials.password as string
        );

        if (!user) {
          throw new Error("Invalid credentials.");
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          userType: user.userType,
        };
      },
    }),
    Google,
    Apple,
  ],
  debug: process.env.NODE_ENV === "development",
  pages: {
    signIn: "/login",
    error: "/login",
  },
});
