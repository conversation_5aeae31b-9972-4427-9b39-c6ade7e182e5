import <PERSON>A<PERSON> from "next-auth"
import NeonA<PERSON>pter from "@auth/neon-adapter"
import { Pool } from "@neondatabase/serverless"
import Credentials from "next-auth/providers/credentials"
import { getUserFromDb } from "./auth-utils"

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: NeonAdapter(new Pool({ connectionString: process.env.DATABASE_URL })),
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string
        token.name = user.name as string
        token.email = user.email as string
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.name = token.name as string
        session.user.email = token.email as string
      }
      return session
    },
  },
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password required")
        }
   
        const user = await getUserFromDb(credentials.email as string, credentials.password as string)
   
        if (!user) {
          throw new Error("Invalid credentials.")
        }
        
        return {
          id: user.id,
          name: user.name,
          email: user.email,
        }
      },
    })
  ],
})