"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { 
  Users, ShoppingCart, TrendingUp, Package, 
  Search, Eye, Download, RefreshCw, X, Check 
} from "lucide-react"
import { toast } from "sonner"
import { MapPin, Truck, CreditCard, ImageIcon } from "lucide-react";
import Image from 'next/image';



interface OrderItem {
  id: string;
  product_id: string;
  product_name: string;
  quantity: number;
  price_at_order: number;
  item_subtotal: number;
  image_url?: string;
  title?: string;
}

interface Order {
  order_id: string;
  id: string;
  order_number: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  total_amount: number;
  status: "pending" | "processing" | "completed" | "cancelled";
  created_at: string;
  order_items: OrderItem[];
  street?: string;
  city?: string;
  province?: string;
  postal_code?: string;
  country?: string;
  delivery_method?: string;
  delivery_fee?: number;
  promotional_discount?: number;
  special_instructions?: string | null;
  custom_design_image_url?: string | null;
}

interface DashboardStats {
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  pendingOrders: number;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

export function AdminDashboard() {
  
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [orders, setOrders] = useState<Order[]>([])
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalRevenue: 0,
    totalCustomers: 0,
    pendingOrders: 0,
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [updatingOrder, setUpdatingOrder] = useState<Order | null>(null)
  const [newStatus, setNewStatus] = useState<string>("")
  const [isUpdating, setIsUpdating] = useState(false)
  

  

  const fetchDashboardData = useCallback(async () => {
  try {
    setLoading(true);
    const response = await fetch("/api/dashboard/orders", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
    });

    if (!response.ok) throw new Error("Failed to fetch orders");

    const data = await response.json();

    if (data.success && data.data) {
      const transformedOrders = data.data.map((order: Order) => ({
        id: order.order_id,
        order_number: order.order_number,
        customer_name: order.customer_name || "N/A",
        customer_email: order.customer_email || "",
        total_amount: Number(order.total_amount),
        status: order.status.toLowerCase(),
        created_at: order.created_at,
        order_items: order.order_items || [],
        street: order.street,
        city: order.city,
        province: order.province,
        postal_code: order.postal_code,
        country: order.country,
        delivery_method: order.delivery_method,
        delivery_fee:
          order.delivery_fee !== undefined
            ? Number(order.delivery_fee)
            : undefined,
        promotional_discount: Number(order.promotional_discount) || 0,
        special_instructions: order.special_instructions,
        custom_design_image_url: order.custom_design_image_url || null,
      }));

      console.log("Transformed Orders:", transformedOrders);
      setOrders(transformedOrders);
      calculateStats(transformedOrders);
    }
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    toast.error("Failed to load orders");
  } finally {
    setLoading(false);
  }
}, [setLoading, setOrders]); // Add other dependencies if used from props/state

useEffect(() => {
  fetchDashboardData();
}, [fetchDashboardData]);


  const calculateStats = (orders: Order[]) => {
    const totalOrders = orders.length
    const totalRevenue = orders.reduce((sum, order) => sum + order.total_amount, 0)
    const uniqueCustomers = new Set(
      orders.map(order => order.customer_email).filter(email => email.trim() !== "")
    ).size
    const pendingOrders = orders.filter(order => order.status === "pending").length

    setStats({
      totalOrders,
      totalRevenue,
      totalCustomers: uniqueCustomers,
      pendingOrders,
    })
  }

const handleUpdateStatus = async () => {
  if (!updatingOrder || !newStatus) return;

  try {
    setIsUpdating(true);
    
    // Convert to uppercase to match database enum
    const normalizedStatus = newStatus.trim().toUpperCase();
    
    const response = await fetch(`/api/dashboard/orders/${updatingOrder.id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ status: normalizedStatus }),
      credentials: "include",
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || data.message || "Failed to update status");
    }

    // Update local state with normalized status
    const updatedOrders = orders.map(order => 
      order.id === updatingOrder.id 
        ? { ...order, status: normalizedStatus.toLowerCase() as Order["status"] } 
        : order
    );
    
    setOrders(updatedOrders);
    calculateStats(updatedOrders);
    
    toast.success(`Order #${updatingOrder.order_number} updated to ${normalizedStatus}`);
    setUpdatingOrder(null);
    setNewStatus("");
  } catch (error) {
    console.error("Error updating status:", error);
    const errorMessage = error instanceof Error 
      ? error.message 
      : "Failed to update order status";
    toast.error(errorMessage);
  } finally {
    setIsUpdating(false);
  }
}

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.order_number.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || order.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-800"
      case "processing": return "bg-blue-100 text-blue-800"
      case "pending": return "bg-yellow-100 text-yellow-800"
      case "cancelled": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="pb-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
          <Card className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Manage orders and monitor business performance</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={fetchDashboardData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium opacity-90">Total Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{stats.totalOrders}</div>
                <ShoppingCart className="h-8 w-8 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium opacity-90">Total Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">${stats.totalRevenue.toLocaleString()}</div>
                <TrendingUp className="h-8 w-8 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium opacity-90">Total Customers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{stats.totalCustomers}</div>
                <Users className="h-8 w-8 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium opacity-90">Pending Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{stats.pendingOrders}</div>
                <Package className="h-8 w-8 opacity-80" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Orders Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Customer Orders</CardTitle>
                <CardDescription>Manage and monitor all customer orders</CardDescription>
              </div>
              <div className="text-sm text-gray-500">
                Showing {filteredOrders.length} of {orders.length} orders
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mt-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by name, email, or order #"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </CardHeader>

          <CardContent>
            <div className="space-y-4">
              {filteredOrders.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  {orders.length === 0 ? "No orders found" : "No matching orders"}
                  <Button variant="ghost" className="mt-2" onClick={fetchDashboardData}>
                    Retry
                  </Button>
                </div>
              ) : (
                filteredOrders.map((order) => (
                  <div
                    key={order.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">Order #{order.order_number}</h3>
                          <Badge className={getStatusColor(order.status)}>
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Customer:</span> {order.customer_name}
                          </div>
                          <div>
                            <span className="font-medium">Email:</span> {order.customer_email}
                          </div>
                          <div>
                            <span className="font-medium">Date:</span> {formatDate(order.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">
                          ${order.total_amount.toFixed(2)}
                        </div>
                      </div>
                    </div>

                    <Separator className="my-3" />

                    {/* Order Items */}
                    <div className="mb-3">
                      <h4 className="font-medium mb-2">Order Items:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {order.order_items?.map((item) => (
                          <div key={item.id} className="bg-gray-50 p-2 rounded text-sm flex gap-2 items-center">
                            {item.image_url ? (
                             <div className="relative w-24 h-24">
  <Image
    src={item.image_url}
    alt={item.title || item.product_name}
    fill
    className="object-cover rounded"
  />
</div>


                            ) : (
                              <div className="w-[50px] h-[50px] bg-gray-200 rounded flex items-center justify-center">
                                <Package className="h-5 w-5 text-gray-400" />
                              </div>
                            )}
                            <div>
                              <div className="font-medium">{item.product_name}</div>
                              <div className="text-gray-600">
                                {item.quantity} × ${item.price_at_order.toFixed(2)} = ${item.item_subtotal.toFixed(2)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                   {/* Expanded Order Details */}
{expandedOrder === order.id && (
  <div className="mt-6 space-y-4 border-t pt-4">
    {/* Order Items */}
    <div className="space-y-3">
      <h4 className="font-medium flex items-center gap-2">
        <Package className="h-4 w-4" />
        Items Ordered
      </h4>
      <div className="grid gap-3">
        {order.order_items.map((item) => (
          <div key={item.id}>
            <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
              <div className="relative h-12 w-12 rounded-md overflow-hidden bg-background border">
                {item.image_url ? (
                  <div className="relative w-24 h-24">
  <Image
    src={item.image_url}
    alt={item.title || item.product_name}
    fill
    className="object-cover"
  />
</div>


                ) : (
                  <div className="h-full w-full flex items-center justify-center">
                    <Package className="h-6 w-6 text-muted-foreground" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">{item.product_name}</p>
                <p className="text-xs text-muted-foreground">
                  Qty: {item.quantity} × {formatCurrency(item.price_at_order)}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-sm">{formatCurrency(item.item_subtotal)}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <Separator />

    {/* Order Details Grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Delivery Information */}
      {order.street && (
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Truck className="h-4 w-4" />
            Delivery Details
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
              <div>
                <p>{order.street}</p>
                <p>
                  {order.city}, {order.province} {order.postal_code}
                </p>
                <p>{order.country}</p>
              </div>
            </div>
            {order.delivery_method && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Method:</span>
                <span className="capitalize">{order.delivery_method.replace("_", " ")}</span>
              </div>
            )}
            {order.delivery_fee && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Delivery Fee:</span>
                <span>{formatCurrency(order.delivery_fee)}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Payment Summary */}
      <div className="space-y-3">
        <h4 className="font-medium flex items-center gap-2">
          <CreditCard className="h-4 w-4" />
          Payment Summary
        </h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Subtotal:</span>
            <span>
              {formatCurrency(order.total_amount - (order.delivery_fee || 0) + (order.promotional_discount || 0))}
            </span>
          </div>
          {order.delivery_fee && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">Delivery:</span>
              <span>{formatCurrency(order.delivery_fee)}</span>
            </div>
          )}
          {(order.promotional_discount || 0) > 0 && (
            <div className="flex justify-between text-green-600">
              <span>Discount:</span>
              <span>-{formatCurrency(order.promotional_discount || 0)}</span>
            </div>
          )}
          <Separator />
          <div className="flex justify-between font-medium">
            <span>Total:</span>
            <span>{formatCurrency(order.total_amount)}</span>
          </div>
        </div>
      </div>
    </div>

    {/* Special Instructions */}
    {order.special_instructions && (
      <>
        <Separator />
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Special Instructions</h4>
          <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
            {order.special_instructions}
          </p>
        </div>
      </>
    )}

    {/* Custom Design Section */}
    {order.custom_design_image_url && (
      <>
        <Separator className="my-4" />
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Custom Design
          </h4>
          <div className="flex flex-col items-center gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="relative w-full max-w-lg aspect-square rounded-lg overflow-hidden border">
              <Image
                src={order.custom_design_image_url}
                alt={`Custom design for order #${order.order_number}`}
                fill
                className="object-contain w-full h-full"
              />
            </div>
            <p className="text-sm text-muted-foreground text-center">
              This is your custom design created for this order
            </p>
          </div>
        </div>
      </>
    )}
  </div>
)}
                    <div className="flex justify-end gap-2">
                       <Button
  variant="outline"
  size="sm"
  onClick={() => setExpandedOrder(expandedOrder === order.id ? null : order.id)}
>
  <Eye className="h-4 w-4 mr-2" />
  {expandedOrder === order.id ? "Hide Details" : "View Details"}
</Button>
      
                      <Button 
                        size="sm"
                        onClick={() => {
                          setUpdatingOrder(order)
                          setNewStatus(order.status)
                        }}
                      >
                        Update Status
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Update Modal */}
      {updatingOrder && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold">
                Update Order #{updatingOrder.order_number}
              </h3>
              <button 
                onClick={() => setUpdatingOrder(null)} 
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Current Status:
                </label>
                <Badge className={getStatusColor(updatingOrder.status)}>
                  {updatingOrder.status.charAt(0).toUpperCase() + updatingOrder.status.slice(1)}
                </Badge>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  New Status:
                </label>
                <select
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:outline-none"
                >
                  <option value="">Select status</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setUpdatingOrder(null)
                    setNewStatus("")
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateStatus}
                  disabled={!newStatus || isUpdating || newStatus === updatingOrder.status}
                >
                  {isUpdating ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Check className="h-4 w-4 mr-2" />
                  )}
                  Update Status
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}