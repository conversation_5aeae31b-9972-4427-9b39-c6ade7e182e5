import { Pool } from "pg"

let pool: Pool | null = null;

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

export function getPool(): Pool {
  if (!pool) {
     pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false,
      },
      // Optimized connection pool settings
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 2000, // Return an error after 5 seconds if connection could not be established
      allowExitOnIdle: true, // Allow the pool to close all connections and exit when there are no more clients
    })

    pool.on('error', (err: Error) => {
      console.error('Unexpected database pool error:', err);
      // Don't set pool to null immediately, let it recover
      setTimeout(() => {
        if (pool) {
          pool.end().then(() => {
            pool = null;
          });
        }
      }, 5000);
    });

    pool.on('connect', (client) => {
      console.log('New database client connected');
      // Set session-level optimizations
      client.query(`
        SET statement_timeout = '30s';
        SET lock_timeout = '10s';
        SET idle_in_transaction_session_timeout = '60s';
      `).catch(err => console.error('Error setting session parameters:', err));
    });

    pool.on('remove', () => {
      console.log('Database client removed from pool');
    });
  }
  return pool;
}

// Release database resources when the application exits
if (typeof process !== 'undefined') {
  process.on('SIGINT', () => {
    if (pool) {
      console.log('Closing database pool due to application shutdown');
      pool.end();
    }
  });
}

const defaultPool = getPool();
export default defaultPool;
