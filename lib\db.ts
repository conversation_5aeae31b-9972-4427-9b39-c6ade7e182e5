import { Pool } from "pg"

let pool: Pool | null = null;

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

export function getPool(): Pool {
  if (!pool) {
     pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false,
      },
      // host: process.env.DATABASE_HOST,
      // user: process.env.DATABASE_USER,
      // password: process.env.DATABASE_PASSWORD,
      // database: process.env.DATABASE_NAME,
      // max: 10,
      // idleTimeoutMillis: 30000,
      // connectionTimeoutMillis: 2000,
      // ssl: {
      //   rejectUnauthorized: false,
      // },
    })
    
    pool.on('error', (err: Error) => {
      console.error('Unexpected database pool error:', err);
      pool = null;
    });
  }
  return pool;
}

// Release database resources when the application exits
if (typeof process !== 'undefined') {
  process.on('SIGINT', () => {
    if (pool) {
      console.log('Closing database pool due to application shutdown');
      pool.end();
    }
  });
}

const defaultPool = getPool();
export default defaultPool;
