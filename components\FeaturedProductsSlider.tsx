"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import Image from "next/image"
import { useKeenSlider } from "keen-slider/react"
import "keen-slider/keen-slider.min.css"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowRight, Eye } from "lucide-react"

type Product = {
  id: string | number
  title: string | string[]
  short_description: string | string[]
  long_description: string | string[]
  price: number | string
  promotional_price_percent?: number | string
  sales_price_percent?: number | string
  image_url: string
  express?: boolean
}

interface FeaturedProductsSliderProps {
  initialFeaturedProducts: Product[]
}

export default function FeaturedProductsSlider({ initialFeaturedProducts }: FeaturedProductsSliderProps) {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>(initialFeaturedProducts || [])
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const slidesPerView = featuredProducts.length > 0 && featuredProducts.length < 4 ? featuredProducts.length : 4

  const handleSlideChange = useCallback((slider: { track: { details: { rel: number } } }) => {
    setCurrentSlide(slider.track.details.rel)
  }, [])

  const sliderOptions = {
    loop: featuredProducts.length > 1,
    mode: "free-snap" as const,
    slides: {
      perView: 1.1,
      spacing: 16,
    },
    breakpoints: {
      "(min-width: 480px)": {
        slides: { perView: 1.5, spacing: 16 },
      },
      "(min-width: 640px)": {
        slides: { perView: 2, spacing: 16 },
      },
      "(min-width: 1024px)": {
        slides: { perView: 3, spacing: 20 },
      },
      "(min-width: 1280px)": {
        slides: { perView: 4, spacing: 24 },
      },
    },
    slideChanged: handleSlideChange,
    created: (slider: any) => {
      setCurrentSlide(slider.track.details.rel)
      // Auto-slide only on larger screens
      if (typeof window !== "undefined" && window.innerWidth >= 768 && featuredProducts.length > 2) {
        const interval = setInterval(() => {
          slider.next()
        }, 4000)
        return () => clearInterval(interval)
      }
    },
    drag: true,
    dragSpeed: 1,
    rubberband: true,
    selector: ".keen-slider__slide",
  }

  const [sliderRef, instanceRef] = useKeenSlider<HTMLDivElement>(sliderOptions)

  useEffect(() => {
    if (initialFeaturedProducts && initialFeaturedProducts.length > 0) {
      setFeaturedProducts(initialFeaturedProducts)
      setIsLoading(false)
      return
    }

    const controller = new AbortController()

    async function fetchFreshProducts() {
      try {
        setIsLoading(true)
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"

        const signal = controller.signal

        const response = await fetch(`${apiUrl}/api/products?featured=true`, {
          cache: "no-store",
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
          signal,
        })

        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)

        const data = await response.json()
        if (data.products && data.products.length > 0) {
          setFeaturedProducts(data.products)
        }
      } catch (error) {
        if (!(error instanceof DOMException && error.name === "AbortError")) {
          console.error("Error fetching featured products:", error)
        }
      } finally {
        setIsLoading(false)
      }
    }

    if (!initialFeaturedProducts || initialFeaturedProducts.length === 0) {
      fetchFreshProducts()
    }

    return () => {
      controller.abort()
    }
  }, [initialFeaturedProducts])

  if (isLoading && featuredProducts.length === 0) {
    return (
      <section className="py-12 px-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Featured Products</h2>
          </div>
          <div className="flex justify-center py-12">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-8 w-32 bg-gray-300 rounded mb-4"></div>
              <div className="h-4 w-48 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (!featuredProducts || featuredProducts.length === 0) {
    return (
      <section className="py-12 px-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Featured Products</h2>
            <Link href="/products" prefetch={true} className="text-blue-600 hover:text-blue-800 flex items-center">
              View All Products <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
          <p className="text-center py-8 text-gray-500">No featured products available at the moment.</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-6 sm:py-12 bg-gray-50">
      <style jsx global>{`
  .keen-slider {
    touch-action: pan-x !important;
    -webkit-overflow-scrolling: touch;
  }
  
  .keen-slider__slide {
    min-height: 0;
    min-width: 0;
  }
  
  @media (max-width: 640px) {
    .keen-slider {
      padding-left: 16px;
      padding-right: 16px;
    }
  }
`}</style>
      <div className="container mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8 px-4">
          <h2 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-0">Featured Products</h2>
          <Link
            href="/products"
            prefetch={true}
            className="text-blue-600 hover:text-blue-800 flex items-center text-sm sm:text-base"
          >
            View All Products <ArrowRight className="ml-2 h-3 h-3 sm:h-4 sm:w-4" />
          </Link>
        </div>
        <div ref={sliderRef} className="keen-slider overflow-visible touch-pan-x" style={{ touchAction: "pan-x" }}>
          {featuredProducts.map((product) => (
            <div key={product.id} className="keen-slider__slide min-w-0 px-2">
              <Card className="overflow-hidden printcloud-card-hover h-full flex flex-col">
                <div className="relative aspect-square">
                  <Link href={`/products/${product.id}`} prefetch={true}>
                    <Image
                      src={product.image_url || "/placeholder.svg"}
                      alt={Array.isArray(product.title) ? product.title[0] : product.title}
                      fill
                      className="object-cover transition-transform group-hover:scale-105 cursor-pointer"
                      priority={true}
                    />
                  </Link>
                  {product.express && <span className="absolute top-2 right-2 express-badge">Express</span>}
                </div>
                <CardContent className="p-3 sm:p-4 flex flex-col h-full">
                  <h3 className="font-semibold text-base sm:text-lg mb-1 line-clamp-2">
                    {Array.isArray(product.title) ? product.title[0] : product.title}
                  </h3>
                  <p className="text-gray-500 text-xs sm:text-sm mb-2 line-clamp-2 flex-grow">
                    {Array.isArray(product.short_description)
                      ? product.short_description[0]
                      : product.short_description}
                  </p>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-auto gap-2">
                    <p className="font-bold text-blue-600 text-sm sm:text-base mb-0">
                      ${Number(product.price || 0).toFixed(2)} CAD
                    </p>
                    <Button asChild size="sm" className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto">
                      <Link
                        href={`/products/${product.id}`}
                        prefetch={true}
                        className="flex items-center justify-center gap-1 sm:gap-2"
                      >
                        <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                        <span className="text-xs sm:text-sm">View</span>
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
        {instanceRef.current && featuredProducts.length > 0 && (
          <div className="flex justify-center mt-4 sm:mt-6 gap-2 px-4 sm:px-0">
            {[...Array(instanceRef.current.track.details.slides.length).keys()].map((idx) => (
              <button
                key={idx}
                onClick={() => instanceRef.current?.moveToIdx(idx)}
                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 touch-manipulation
                  ${currentSlide === idx ? "bg-blue-600 scale-125 shadow-lg" : "bg-gray-300"}
                  border-none outline-none hover:bg-blue-400`}
                aria-label={`Go to slide ${idx + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
