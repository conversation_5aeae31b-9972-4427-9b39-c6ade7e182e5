'use server'

import nodemailer from 'nodemailer'

interface OrderItem {
  name: string
  quantity: number
  price: number
  image: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
})

export async function sendOrderConfirmation({
  orderNumber,
  items,
  subtotalAmount,
  promotionalDiscount,
  hasPromotionalItems,
  deliveryFee,
  totalAmount,
  deliveryMethod,
  deliveryAddress,
  estimatedDeliveryTime,
  customerName,
  customerEmail,
  customerPhone,
  specialInstructions,
}: {
  orderNumber: string
  items: OrderItem[]
  subtotalAmount: number
  promotionalDiscount: number
  hasPromotionalItems: boolean
  deliveryFee: number
  totalAmount: number
  deliveryMethod: 'PICKUP' | 'DELIVERY'
  deliveryAddress?: DeliveryAddress
  estimatedDeliveryTime: string
  customerName: string
  customerEmail: string
  customerPhone: string
  specialInstructions?: string
}) {
  try {
    const mailOptions = {
      from: 'PrintCloud <<EMAIL>>',
      to: customerEmail,
      subject: `Order Confirmation - ${orderNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">Order Confirmation</h1>
          <p>Thank you for your order!</p>
          
          <div style="margin: 20px 0; padding: 20px; background-color: #f8fafc; border-radius: 8px;">
            <h2 style="color: #1e40af;">Order Details</h2>
            <p><strong>Order Number:</strong> ${orderNumber}</p>
            <p><strong>Delivery Method:</strong> ${deliveryMethod}</p>
            ${deliveryMethod === 'DELIVERY' && deliveryAddress ? `
              <p><strong>Delivery Address:</strong></p>
              <p>${deliveryAddress.street}<br>
              ${deliveryAddress.city}, ${deliveryAddress.province}<br>
              ${deliveryAddress.postalCode}</p>
            ` : ''}
            <p><strong>Estimated ${deliveryMethod === 'DELIVERY' ? 'Delivery' : 'Pickup'} Time:</strong> ${estimatedDeliveryTime}</p>
          </div>

          <div style="margin: 20px 0;">
            <h2 style="color: #1e40af;">Order Items</h2>
            ${items.map(item => `
              <div style="margin: 10px 0; padding: 10px; background-color: #f8fafc; border-radius: 4px;">
                <p><strong>${item.name}</strong></p>
                <p>Quantity: ${item.quantity}</p>
                <p>Price: $${item.price.toFixed(2)}</p>
                <p>Subtotal: $${(item.price * item.quantity).toFixed(2)}</p>
              </div>
            `).join('')}
          </div>

          <div style="margin: 20px 0; padding: 20px; background-color: #f8fafc; border-radius: 8px;">
            <h2 style="color: #1e40af;">Order Summary</h2>
            <p><strong>Subtotal:</strong> $${subtotalAmount.toFixed(2)}</p>
            ${hasPromotionalItems ? `<p><strong>Promotional Discount:</strong> -$${promotionalDiscount.toFixed(2)}</p>` : ''}
            ${deliveryMethod === 'DELIVERY' ? `<p><strong>Delivery Fee:</strong> $${deliveryFee.toFixed(2)}</p>` : ''}
            <p><strong>Total Amount:</strong> $${totalAmount.toFixed(2)}</p>
          </div>

          ${specialInstructions ? `
            <div style="margin: 20px 0; padding: 20px; background-color: #f8fafc; border-radius: 8px;">
              <h2 style="color: #1e40af;">Special Instructions</h2>
              <p>${specialInstructions}</p>
            </div>
          ` : ''}

          <div style="margin: 20px 0; padding: 20px; background-color: #f8fafc; border-radius: 8px;">
            <h2 style="color: #1e40af;">Customer Information</h2>
            <p><strong>Name:</strong> ${customerName}</p>
            <p><strong>Email:</strong> ${customerEmail}</p>
            <p><strong>Phone:</strong> ${customerPhone}</p>
          </div>

          <div style="margin: 20px 0; text-align: center;">
            <p>If you have any questions about your order, please contact us at:</p>
            <p>Phone: ************</p>
            <p>Email: <EMAIL></p>
          </div>
        </div>
      `
    }

    const info = await transporter.sendMail(mailOptions)
    return { success: true, data: info }
  } catch (error) {
    console.error('Error sending email:', error)
    return { success: false, error }
  }
} 