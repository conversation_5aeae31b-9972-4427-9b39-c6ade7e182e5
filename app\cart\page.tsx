"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Trash2, Plus, Minus, ArrowRight, ShoppingCart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { useCartStore } from "@/store/cart-store"
import { toast } from "sonner"

export default function CartPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isCheckingOut, setIsCheckingOut] = useState(false)

  const { items, removeItem, updateQuantity, clearCart, getTotal, getItemsCount } = useCartStore()

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return
    updateQuantity(id, newQuantity)
  }

  const handleRemoveItem = (id: string) => {
    removeItem(id)
    toast("Item removed", {
      description: "The item has been removed from your cart.",
    })
  }

  const handleCheckout = () => {
    if (!session) {
      toast("Sign in required", {
        description: "You need to be signed in to checkout.",
      })
      router.push("/login")
      return
    }

    setIsCheckingOut(true)

    // Simulate checkout process
    setTimeout(() => {
      router.push("/checkout")
    }, 1000)
  }

  const totalItems = getItemsCount()
  const subtotal = getTotal()
  const shipping = subtotal > 149 ? 0 : 15
  const total = subtotal + shipping

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Your Cart</h1>

      {items.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center mb-4">
            <ShoppingCart className="h-16 w-16 text-gray-300" />
          </div>
          <h2 className="text-2xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-6">Looks like you haven&apos;t added any products to your cart yet.</p>
          <Button asChild>
            <Link href="/products">Continue Shopping</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6">
                <div className="hidden md:grid grid-cols-12 gap-4 mb-4 text-sm font-medium text-gray-500">
                  <div className="col-span-6">Product</div>
                  <div className="col-span-2 text-center">Price</div>
                  <div className="col-span-2 text-center">Quantity</div>
                  <div className="col-span-2 text-center">Total</div>
                </div>

                <Separator className="mb-6" />

                {items.map((item) => (
                  <div key={item.id} className="mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                      <div className="col-span-6 flex items-center">
                        <div className="relative w-20 h-20 mr-4 border rounded-md overflow-hidden">
                          <Image src={item.image || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                        </div>
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <button
                            onClick={() => handleRemoveItem(item.id)}
                            className="text-sm text-red-600 flex items-center mt-1"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Remove
                          </button>
                        </div>
                      </div>

                      <div className="col-span-2 text-center">
                        <div className="md:hidden text-sm text-gray-500 mb-1">Price:</div>${item.price.toFixed(2)}
                      </div>

                      <div className="col-span-2">
                        <div className="md:hidden text-sm text-gray-500 mb-1">Quantity:</div>
                        <div className="flex items-center justify-center">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="mx-2 w-8 text-center">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="col-span-2 text-center font-medium">
                        <div className="md:hidden text-sm text-gray-500 mb-1">Total:</div>$
                        {(item.price * item.quantity).toFixed(2)}
                      </div>
                    </div>

                    {items.indexOf(item) < items.length - 1 && <Separator className="my-6" />}
                  </div>
                ))}
              </CardContent>
              <CardFooter className="flex justify-between p-6 pt-0">
                <Button variant="outline" onClick={() => clearCart()}>
                  Clear Cart
                </Button>
                <Button asChild>
                  <Link href="/products">Continue Shopping</Link>
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-4">Order Summary</h2>

                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal ({totalItems} items)</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-600">Shipping</span>
                    <span>{shipping === 0 ? "Free" : `$${shipping.toFixed(2)}`}</span>
                  </div>

                  <Separator />

                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>${total.toFixed(2)}</span>
                  </div>

                  <div className="text-xs text-gray-500">
                    {shipping === 0 ? (
                      <p>Your order qualifies for free shipping!</p>
                    ) : (
                      <p>Add ${(149 - subtotal).toFixed(2)} more to qualify for free shipping.</p>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="p-6 pt-0">
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  onClick={handleCheckout}
                  disabled={isCheckingOut}
                >
                  {isCheckingOut ? "Processing..." : "Checkout"}
                  {!isCheckingOut && <ArrowRight className="ml-2 h-4 w-4" />}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}
