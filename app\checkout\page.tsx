"use client"

import { useAuth } from "@/hooks/useAuth"
import { useCartStore } from "@/store/cart"
import { canadianProvinces } from "@/utils/states"
import { ArrowLeft } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useState, useEffect, Suspense } from "react"
import { toast } from "sonner"
import { Button } from "@/components/ui/button"
import AddressAutocomplete from "@/components/address-autocomplete"
import { createOrder } from "../actions/order"

function CheckoutContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const queryDeliveryMethod = searchParams.get("deliveryMethod")
  const [deliveryMethod, setDeliveryMethod] = useState(
    queryDeliveryMethod || "PICKUP"
  )
  const cart = useCartStore()
  const { isAuthenticated, session } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedProvince, setSelectedProvince] = useState("")
  const [formData, setFormData] = useState({
    street: "",
    city: "",
    province: "",
    postalCode: "",
  })

  // Delivery fee calculation
  const subtotal = cart.getSubtotalPrice()
  const deliveryFeeAmount = 3.99
  const freeDeliveryThreshold = 75

  const envDeliveryFee = process.env.NEXT_PUBLIC_DELIVERY_FEE
    ? Number(process.env.NEXT_PUBLIC_DELIVERY_FEE)
    : deliveryFeeAmount

  const envFreeDeliveryThreshold = process.env.NEXT_PUBLIC_FREE_DELIVERY_THRESHOLD
    ? Number(process.env.NEXT_PUBLIC_FREE_DELIVERY_THRESHOLD)
    : freeDeliveryThreshold

  const deliveryFee =
    deliveryMethod === "DELIVERY" && subtotal < envFreeDeliveryThreshold
      ? envDeliveryFee
      : 0

  const promoDiscount = cart.getPromotionalDiscount()
  const totalPrice = subtotal + deliveryFee - promoDiscount

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedDeliveryMethod = localStorage.getItem("pending_delivery_method")
      if (
        storedDeliveryMethod &&
        (storedDeliveryMethod === "DELIVERY" || storedDeliveryMethod === "PICKUP")
      ) {
        setDeliveryMethod(storedDeliveryMethod)
        cart.setDeliveryMethod(storedDeliveryMethod as "DELIVERY" | "PICKUP")
        localStorage.removeItem("pending_delivery_method")
      }
    }
  }, [cart])

  useEffect(() => {
    if (!isAuthenticated) {
      const returnUrl = encodeURIComponent("/checkout")
      router.push(`/login?returnUrl=${returnUrl}`)
    }
  }, [isAuthenticated, router])

  if (!isAuthenticated) {
    return null
  }

  console.log(session);

  const handleProvinceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedProvince(e.target.value)
  }

  const handleAddressSelect = (address: {
    street: string
    city: string
    province: string
    postalCode: string
  }) => {
    setFormData(address)
    setSelectedProvince(address.province)
  }

  const handleSubmit = async () => {
    try {
      setIsProcessing(true)

      if (
        deliveryMethod === "DELIVERY" &&
        (!formData.street ||
          !formData.city ||
          !formData.province ||
          !formData.postalCode)
      ) {
        throw new Error("Please fill in all required address fields")
      }

      if (!cart.items || cart.items.length === 0) {
        throw new Error("Your cart is empty")
      }

      const specialInstructions = document.getElementById(
        "specialInstructions"
      ) as HTMLTextAreaElement


      

      const result = await createOrder({
        userId: Number(session?.user?.id),
        items: cart.items.map(item => ({
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          image: item.image
        })),
        deliveryMethod: deliveryMethod as "PICKUP" | "DELIVERY",
        deliveryAddress: deliveryMethod === "DELIVERY" ? {
          street: formData.street.trim(),
          city: formData.city.trim(),
          province: formData.province.trim(),
          postalCode: formData.postalCode.replace(/\s/g, "").toUpperCase()
        } : undefined,
        specialInstructions: specialInstructions?.value || "",
        subtotal,
        deliveryFee,
        promotionalDiscount: promoDiscount,
        totalAmount: totalPrice
      })

      if (result.success) {
        cart.clearCart()
        toast.success("Order placed successfully!")
        router.push(`/order-success?orderId=${result.orderId}`)
      } else {
        throw new Error("Failed to create order")
      }
    } catch (error) {
      console.error("Order submission error:", error)
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to place order. Please try again."
      )
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 mt-[120px]">
      {isProcessing && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"></div>
              <h3 className="text-xl font-semibold mb-2">Processing Order</h3>
              <p className="text-gray-600 text-center">
                Please wait while we process your order...
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-2 mb-8">
          <Link href="/cart">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-600 hover:text-blue-600"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Cart
            </Button>
          </Link>
        </div>

        <h1 className="text-2xl md:text-3xl font-bold mb-8">Checkout</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Order Summary</h2>
            <div className="space-y-4">
              {cart.items.map((item) => (
                <div key={item.id} className="flex items-center gap-4">
                  <div className="relative w-16 h-16">
                    <Image
                      src={item.image}
                      alt={item.name}
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">{item.name}</h3>
                    <p className="text-sm text-gray-600">
                      Qty: {item.quantity}
                    </p>
                    <p className="text-blue-600 font-medium">
                      ${(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
              <div className="space-y-4">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>

                {cart.hasPromotionalItems() && (
                  <div>
                    <div className="flex justify-between text-green-600">
                      <span>Promotional Discount</span>
                      <span>
                        {(deliveryMethod === "PICKUP" && subtotal >= 15) || (deliveryMethod === "DELIVERY" && subtotal >= 30)
                          ? `-$${promoDiscount.toFixed(2)}`
                          : "$0.00"}
                      </span>
                    </div>
                    {deliveryMethod === "PICKUP" && subtotal < 15 && (
                      <div className="text-sm text-orange-500 mt-1 mb-2 bg-orange-50 p-2 rounded">
                        <div className="text-xs uppercase font-medium mb-1 text-gray-700">
                          only eligible for pickup orders over <span className="text-red-600 text-base font-bold">$15</span>
                        </div>
                        <span className="font-medium">Almost there!</span> Spend
                        ${(15 - subtotal).toFixed(2)} more to get $3 OFF!
                      </div>
                    )}
                    {deliveryMethod === "DELIVERY" && subtotal < 30 && (
                      <div className="text-sm text-orange-500 mt-1 mb-2 bg-orange-50 p-2 rounded">
                        <div className="text-xs uppercase font-medium mb-1 text-gray-700">
                          only eligible for delivery orders over <span className="text-red-600 text-base font-bold">$30</span>
                        </div>
                        <span className="font-medium">Almost there!</span> Spend
                        ${(30 - subtotal).toFixed(2)} more to get $3 OFF!
                      </div>
                    )}
                  </div>
                )}

                {deliveryMethod === "DELIVERY" && (
                  <div className="flex justify-between text-gray-600">
                    <span>Delivery Fee</span>
                    {subtotal < envFreeDeliveryThreshold ? (
                      <span>${envDeliveryFee.toFixed(2)}</span>
                    ) : (
                      <span className="text-green-600">Free</span>
                    )}
                  </div>
                )}

                <div className="border-t pt-4">
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span className="text-blue-600">
                      ${totalPrice.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Delivery Form */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6">
              {deliveryMethod === "DELIVERY"
                ? "Delivery Details"
                : "Pickup Details"}
            </h2>
            <div className="space-y-4">
              {deliveryMethod === "DELIVERY" ? (
                <>
                  <div>
                    <label
                      htmlFor="street"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Street Address
                    </label>
                    <AddressAutocomplete
                      onAddressSelect={handleAddressSelect}
                      value={formData.street}
                      onChange={(value) => setFormData(prev => ({ ...prev, street: value }))}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="province"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Province
                      </label>
                      <select
                        id="province"
                        name="province"
                        required
                        value={selectedProvince}
                        onChange={handleProvinceChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent bg-white"
                      >
                        <option value="">Select Province</option>
                        {canadianProvinces.map((province) => (
                          <option key={province.name} value={province.name}>
                            {province.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label
                        htmlFor="city"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        City
                      </label>
                      <input
                        type="text"
                        id="city"
                        name="city"
                        required
                        value={formData.city}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            city: e.target.value,
                          }))
                        }
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        placeholder="Enter your city"
                      />
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="postalCode"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Postal Code
                    </label>
                    <input
                      type="text"
                      id="postalCode"
                      name="postalCode"
                      required
                      value={formData.postalCode}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          postalCode: e.target.value,
                        }))
                      }
                      pattern="[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d"
                      placeholder="A1A 1A1"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Format: A1A 1A1 (space optional)
                    </p>
                  </div>
                </>
              ) : (
                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Your order will be ready for pickup in approximately 25-30
                    minutes.
                  </p>
                </div>
              )}

              <div>
                <label
                  htmlFor="specialInstructions"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Special Instructions{" "}
                  <span className="text-gray-500 text-xs">(Optional)</span>
                </label>
                <textarea
                  id="specialInstructions"
                  name="specialInstructions"
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                  placeholder="Add any special instructions for your order here"
                />
              </div>

              <Button
                onClick={handleSubmit}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={isProcessing}
              >
                {isProcessing ? "Processing..." : "Place Order"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }
    >
      <CheckoutContent />
    </Suspense>
  )
} 