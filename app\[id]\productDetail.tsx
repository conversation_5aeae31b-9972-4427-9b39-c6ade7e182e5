'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import Link from 'next/link';
import Image from 'next/image';
import { Product } from '../../types';
import { useCartStore } from '@/store/cart-store';

interface ProductDetailProps {
  product: Product;
}

export default function ProductDetail({ product }: ProductDetailProps) {
  const { addItem } = useCartStore();
  const [mainImage, setMainImage] = useState<string>('');

  useEffect(() => {
    if (product) {
      setMainImage(product.image);
    }
  }, [product]);

  const galleryImages = product.galleryImages || [];

  const handleAddToCart = () => {
    const productPrice = Array.isArray(product.price) ? product.price[0] : product.price;
    // Ensure price is a number
    const price = typeof productPrice === 'string' ? parseFloat(productPrice) : productPrice;
    
    addItem({
      id: product.id.toString(),
      name: Array.isArray(product.title) ? product.title[0] : product.title,
      price: price,
      quantity: 1,
      image: product.image,
    });
  };

  return (
    <>
      <Header />

      <main className="max-w-6xl mx-auto px-4 py-10 font-sans">
        {/* Breadcrumb */}
        <nav className="mb-6 text-sm text-gray-600">
          <ol className="flex space-x-2">
            <li><Link href="/" className="text-blue-600 hover:underline">Home</Link></li>
            <li>/</li>
            <li className="text-black">{Array.isArray(product.title) ? product.title[0] : product.title}</li>
          </ol>
        </nav>

        {/* Product Section */}
        <div className="bg-white rounded-xl shadow-md p-6">
          <div className="flex flex-col gap-12">

            {/* Left Side: Main Image and Thumbnails */}
            <div className="flex flex-col gap-6">
              {/* Thumbnails */}
              <div className="flex gap-2 overflow-x-auto">
                {[product.image, ...galleryImages].map((img, idx) => (
                  <div
                    key={idx}
                    onClick={() => setMainImage(img)}
                    className={`w-20 h-20 relative cursor-pointer transition-all ease-in-out duration-300 border-2 rounded-lg ${
                      mainImage === img ? 'border-blue-500' : 'border-gray-300 hover:border-gray-500'
                    }`}
                  >
                    <Image
                      src={img}
                      alt={`Thumbnail ${idx}`}
                      fill
                      sizes="80px"
                      className="object-cover rounded-lg"
                    />
                  </div>
                ))}
              </div>

              {/* Main Image */}
              <div className="bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center h-[300px] md:h-[400px] shadow-lg relative">
                <Image
                  src={mainImage}
                  alt={Array.isArray(product.title) ? product.title[0] : product.title}
                  fill
                  sizes="(max-width: 768px) 100vw, 800px"
                  className="object-contain transition-transform duration-500 ease-in-out transform hover:scale-105"
                />
              </div>
            </div>

            {/* Right Side: Product Info */}
            <div className="flex flex-col space-y-6">
              <div>
                <h1 className="text-3xl font-semibold text-black mb-2">
                  {Array.isArray(product.title) ? product.title[0] : product.title}
                </h1>
                
                <p className="text-black mb-6 leading-relaxed">
                  {Array.isArray(product.shortDesc) ? product.shortDesc[0] : product.shortDesc}
                </p>
                <p className="text-xl text-blue-700 font-medium mb-4">
                  ${Array.isArray(product.price) ? product.price[0] : product.price}
                </p>
              </div>

              <button
                onClick={handleAddToCart}
                className="self-start bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-full text-lg font-semibold transition-all shadow-sm hover:shadow-md focus:ring-4 focus:ring-blue-300"
              >
                + Add to Cart
              </button>
            </div>
          </div>

          {/* Long Description */}
          {(Array.isArray(product.longDesc) ? product.longDesc[0] : product.longDesc) && (
            <section className="mt-12">
              <h2 className="text-2xl font-semibold text-black mb-4">Product Details</h2>
              <div className="text-black columns-1 md:columns-2 gap-8 space-y-4 leading-relaxed">
                <p className="text-base">
                  {Array.isArray(product.longDesc) ? product.longDesc[0] : product.longDesc}
                </p>
              </div>
            </section>
          )}
        </div>
      </main>

      <Footer />
    </>
  );
} 