'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useEffect } from 'react';
import { Product } from '../types';
import { useCartStore } from '../store/cart-store';

interface ProductsProps {
  products: Product[];
}

export default function Products({ products }: ProductsProps) {
  const { addItem, items } = useCartStore();

  useEffect(() => {
    console.log("Cart updated:", items);
  }, [items]);

  if (!Array.isArray(products)) {
    return <p>Loading products or no products available.</p>;
  }

  const handleAddToCart = (product: Product) => {
    const productPrice = Array.isArray(product.price) ? product.price[0] : product.price;
    // Ensure price is a number
    const price = typeof productPrice === 'string' ? parseFloat(productPrice) : productPrice;
    
    addItem({
      id: product.id.toString(),
      name: Array.isArray(product.title) ? product.title[0] : product.title,
      price: price,
      quantity: 1,
      image: product.image,
    });
  };

  return (
    <div className="px-4 md:px-6 lg:px-16 py-6 bg-gray-100">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product, index) => (
          <Link href={`/${product.id}`} key={index}>
            <div className="bg-white rounded-lg shadow-lg hover:shadow-2xl transition duration-300 transform hover:scale-[1.02] cursor-pointer h-[480px] flex flex-col justify-between p-4">
              <div className="relative w-full h-[250px]">
                <Image
                  src={product.image}
                  alt={Array.isArray(product.title) ? product.title[0] : product.title}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                  className="rounded-lg object-cover"
                />
              </div>
              <h2 className="text-xl font-semibold mt-3 text-gray-800">
                {Array.isArray(product.title) ? product.title[0] : product.title}
              </h2>
              <p className="text-gray-600 mt-2 line-clamp-2 overflow-hidden">
                {Array.isArray(product.shortDesc) ? product.shortDesc[0] : product.shortDesc}
              </p>

              <p className="text-lg font-semibold text-green-700 mt-2">
                $ {Array.isArray(product.price) ? product.price[0] : product.price}
              </p>
              <button
                onClick={(e) => {
                 e.stopPropagation(); // Prevents the click from bubbling up to the Link
                 e.preventDefault(); //  Stops Link navigation
                 handleAddToCart(product);
              }}
                className="mt-4 w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors duration-300"
              >
                Add to Cart
              </button>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
} 