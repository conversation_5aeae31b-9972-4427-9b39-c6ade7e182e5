"use client"

import { useState, useMemo } from "react"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, ShoppingCart, Star, Check, Heart, Share2, <PERSON>rkles, Shield, Truck } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { useRouter } from "next/navigation"
import DesignDialog from "@/components/DesignDialog"

interface Product {
  id: number
  title: string
  short_description: string
  long_description: string | string[]
  price: string | number
  image_url: string
  gallery_urls: string[]
  created_at?: string
}

interface ProductDetailProps {
  product: Product
}

console.log("premium");

const ProductGalleryThumbnails = ({
  product,
  selectedImage,
  setSelectedImage,
}: ProductDetailProps & {
  selectedImage: string
  setSelectedImage: (img: string) => void
}) => (
  <div className="space-y-4">
    <div className="grid grid-cols-4 gap-3">
      {[product.image_url, ...(product.gallery_urls || [])].map((img, index) => (
        <button
          key={img}
          onClick={() => setSelectedImage(img)}
          className={`relative aspect-square overflow-hidden rounded-2xl transition-all duration-300 border-2 ${
            selectedImage === img
              ? "border-emerald-400 ring-2 ring-emerald-400/30 scale-105"
              : "border-slate-700 hover:border-emerald-500 hover:scale-105"
          }`}
        >
          <Image
            src={img || "/placeholder.svg"}
            alt={`${product.title} view ${index + 1}`}
            fill
            className="object-cover"
          />
          {selectedImage === img && (
            <div className="absolute inset-0 bg-emerald-400/10 flex items-center justify-center">
              <div className="bg-emerald-400 rounded-full p-1">
                <Check className="h-3 w-3 text-slate-900" />
              </div>
            </div>
          )}
        </button>
      ))}
    </div>
  </div>
)

const extractHighlights = (longDescription: string | string[]): string[] => {
  if (!longDescription) return []
  const descriptionText = Array.isArray(longDescription) ? longDescription.join("\n") : longDescription
  if (descriptionText.includes("|")) {
    return descriptionText
      .split("|")
      .map((item) => item.trim())
      .filter((item) => item.length > 0)
  }
  if (descriptionText.includes("\n")) {
    return descriptionText
      .split("\n")
      .map((item) => item.trim())
      .filter((item) => item.length > 0)
  }
  if (descriptionText.includes("High Quality Print") && descriptionText.includes("Customization Available")) {
    return ["High Quality Print", "Customization Available", "Fast Processing", "Premium Materials"]
  }
  return descriptionText
    .split(/[.!?]/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}

const CheckoutSection = ({ product }: ProductDetailProps) => {
  const router = useRouter();
  const [isDesignDialogOpen, setIsDesignDialogOpen] = useState(false);
  
  const handleContinueToCheckout = () => {
    setIsDesignDialogOpen(true);
  };

  const handleDesignComplete = (designId: string) => {
    localStorage.setItem(
      "selectedProduct",
      JSON.stringify({
        id: product.id,
        title: product.title,
        price: product.price,
        image_url: product.image_url,
      }),
    )

    router.push(`/checkout?product=${product.id}&designId=${designId}`);
  };

  return (
    <div className="space-y-6">
      <Button
        onClick={handleContinueToCheckout}
        size="lg"
        className="w-full h-16 text-lg font-bold bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-slate-900 shadow-xl hover:shadow-2xl transition-all duration-300 rounded-2xl"
      >
        <ShoppingCart className="mr-3 h-6 w-6" />
        Add to Cart - Continue
      </Button>

      <div className="grid grid-cols-2 gap-4">
        <Button
          variant="outline"
          size="lg"
          className="h-14 border-slate-600 text-slate-300 hover:text-emerald-400 hover:border-emerald-400 bg-slate-800/50 rounded-xl"
        >
          <Heart className="mr-2 h-5 w-5" />
          Wishlist
        </Button>
        <Button
          variant="outline"
          size="lg"
          className="h-14 border-slate-600 text-slate-300 hover:text-emerald-400 hover:border-emerald-400 bg-slate-800/50 rounded-xl"
        >
          <Share2 className="mr-2 h-5 w-5" />
          Share
        </Button>
      </div>

      {/* Trust Badges */}
      <div className="grid grid-cols-3 gap-4 pt-4">
        <div className="text-center space-y-2">
          <div className="bg-emerald-500/20 rounded-full p-3 w-fit mx-auto">
            <Shield className="h-5 w-5 text-emerald-400" />
          </div>
          <p className="text-xs text-slate-400">Secure Payment</p>
        </div>
        <div className="text-center space-y-2">
          <div className="bg-emerald-500/20 rounded-full p-3 w-fit mx-auto">
            <Truck className="h-5 w-5 text-emerald-400" />
          </div>
          <p className="text-xs text-slate-400">Fast Shipping</p>
        </div>
        <div className="text-center space-y-2">
          <div className="bg-emerald-500/20 rounded-full p-3 w-fit mx-auto">
            <Sparkles className="h-5 w-5 text-emerald-400" />
          </div>
          <p className="text-xs text-slate-400">Premium Quality</p>
        </div>
      </div>
      
      <DesignDialog
        isOpen={isDesignDialogOpen}
        onClose={() => setIsDesignDialogOpen(false)}
        productId={product.id.toString()}
        productName={product.title}
        onDesignComplete={handleDesignComplete}
      />
    </div>
  )
}

const ProductDetailPremium = ({ product }: ProductDetailProps) => {
  const [selectedImage, setSelectedImage] = useState(product.image_url)

  const formattedTime = product.created_at
    ? new Date(product.created_at).toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      })
    : "New Arrival"

  const productHighlights = useMemo(() => extractHighlights(product.long_description), [product.long_description])

  const formattedDescription = useMemo(() => {
    const descriptionText = Array.isArray(product.long_description)
      ? product.long_description.join("\n")
      : product.long_description
    return descriptionText
  }, [product.long_description])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-8">
             <Link
  href="/products"
  prefetch={true}
  className="flex items-center gap-1.5 px-3 py-1.5 text-sm bg-white text-green-700 hover:bg-green-100 border border-green-600 rounded-md transition-colors duration-200 mb-4 w-fit"
>
  <ArrowLeft className="h-4 w-4" />
  <span>Back to Products</span>
</Link>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 pb-20">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-16">
          {/* Left Column - Images */}
          <div className="space-y-8">
            {/* Main Product Image */}
            <div className="relative aspect-square overflow-hidden rounded-3xl bg-slate-800 shadow-2xl border border-slate-700">
              <Image
                src={selectedImage || "/placeholder.svg"}
                alt={product.title}
                className="object-cover transition-transform duration-700 hover:scale-110"
                fill
                priority
              />
              <div className="absolute top-6 left-6">
                <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-slate-900 font-bold px-4 py-2 text-sm rounded-full">
                  {formattedTime}
                </Badge>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/20 to-transparent" />
            </div>

            {/* Thumbnail Gallery */}
            <ProductGalleryThumbnails
              product={product}
              selectedImage={selectedImage}
              setSelectedImage={setSelectedImage}
            />
          </div>

          {/* Right Column - Product Info */}
          <div className="space-y-8">
            {/* Product Header */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Badge className="border-emerald-400 text-emerald-400 font-semibold px-3 py-1">
                  PRINTCLOUD DESIGN
                </Badge>
                <div className="h-1 w-1 bg-slate-600 rounded-full" />
                <span className="text-slate-400 text-sm">Premium Collection</span>
              </div>

              <h1 className="text-5xl xl:text-6xl font-bold text-white leading-tight tracking-tight">
                {product.title}
              </h1>

              <p className="text-xl text-slate-300 leading-relaxed font-light">{product.short_description}</p>

              {/* Rating */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${i < 4 ? "fill-emerald-400 text-emerald-400" : "text-slate-600"}`}
                    />
                  ))}
                </div>
                <span className="text-slate-400">(4.8)</span>
                <div className="h-1 w-1 bg-slate-600 rounded-full" />
                <span className="text-slate-400">127 reviews</span>
              </div>
            </div>

            <Separator className="bg-slate-700" />

            {/* Price */}
            <div className="space-y-3">
              <div className="flex items-baseline gap-3">
                <span className="text-5xl font-bold text-white">
                  $
                  {typeof product.price === "string"
                    ? Number.parseFloat(product.price).toFixed(2)
                    : product.price.toFixed(2)}
                </span>
                <span className="text-xl text-slate-400 font-medium">CAD</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 bg-emerald-400 rounded-full" />
                <p className="text-emerald-400 font-medium">Free shipping on orders over $50</p>
              </div>
            </div>

            <Separator className="bg-slate-700" />

            {/* Product Highlights */}
            {productHighlights.length > 0 && (
              <Card className="bg-gradient-to-br from-slate-800 to-slate-700 border-slate-600 shadow-xl">
                <CardContent className="p-8">
                  <h3 className="font-bold text-xl mb-6 text-white flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-emerald-400" />
                    Premium Features
                  </h3>
                  <div className="grid gap-4">
                    {productHighlights.map((highlight, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <div className="bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full p-1.5 mt-1">
                          <Check className="h-4 w-4 text-slate-900" />
                        </div>
                        <span className="text-slate-200 font-medium text-lg">{highlight}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Checkout Section */}
            <CheckoutSection product={product} />

            <Separator className="bg-slate-700" />

            {/* Product Description */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-white">Product Details</h3>
              <div className="prose prose-invert max-w-none">
                <p className="text-slate-300 leading-relaxed text-lg whitespace-pre-line">{formattedDescription}</p>
              </div>
            </div>

            {/* Additional Info */}
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-8">
                <div className="grid gap-6">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 font-medium">Processing Time</span>
                    <span className="text-white font-bold">2-3 business days</span>
                  </div>
                  <Separator className="bg-slate-700" />
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 font-medium">Shipping</span>
                    <span className="text-white font-bold">5-7 business days</span>
                  </div>
                  <Separator className="bg-slate-700" />
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 font-medium">Return Policy</span>
                    <span className="text-emerald-400 font-bold">30 days guarantee</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductDetailPremium
