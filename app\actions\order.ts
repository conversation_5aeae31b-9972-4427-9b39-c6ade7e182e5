'use server'

import { Pool } from "@neondatabase/serverless"
import { v4 as uuidv4 } from 'uuid'

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

export async function createOrder({
  userId,
  items,
  deliveryMethod,
  deliveryAddress,
  specialInstructions,
  deliveryFee,
  promotionalDiscount,
  totalAmount,
}: {
  userId: number
  items: OrderItem[]
  deliveryMethod: 'PICKUP' | 'DELIVERY'
  deliveryAddress?: DeliveryAddress
  specialInstructions?: string
  subtotal: number
  deliveryFee: number
  promotionalDiscount: number
  totalAmount: number
}) {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL })
  const client = await pool.connect()

  try {
    await client.query('BEGIN')

    // Generate unique order number
    const orderNumber = `ORD-${uuidv4().slice(0, 8).toUpperCase()}`

    // Create order
    const orderResult = await client.query(
      `INSERT INTO orders (
        user_id, order_number, total_amount, delivery_method,
        delivery_fee, promotional_discount, special_instructions
      ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`,
      [
        userId,
        orderNumber,
        totalAmount,
        deliveryMethod,
        deliveryFee,
        promotionalDiscount,
        specialInstructions || null
      ]
    )

    const orderId = orderResult.rows[0].id

    // Create order items
    for (const item of items) {
      await client.query(
        `INSERT INTO order_items (
          order_id, product_id, product_name,
          quantity, price_at_order, item_subtotal
        ) VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          orderId,
          item.id,
          item.name,
          item.quantity,
          item.price,
          item.price * item.quantity
        ]
      )
    }

    // Create delivery address if delivery method is DELIVERY
    if (deliveryMethod === 'DELIVERY' && deliveryAddress) {
      await client.query(
        `INSERT INTO delivery_addresses (
          order_id, street, city, province, postal_code
        ) VALUES ($1, $2, $3, $4, $5)`,
        [
          orderId,
          deliveryAddress.street,
          deliveryAddress.city,
          deliveryAddress.province,
          deliveryAddress.postalCode
        ]
      )
    }

    await client.query('COMMIT')

    return {
      success: true,
      orderId,
      orderNumber
    }
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('Error creating order:', error)
    throw new Error('Failed to create order')
  } finally {
    client.release()
    await pool.end()
  }
} 