'use server'

import { getPool } from '@/lib/db'
import { getUserInfo } from '@/lib/db-utils'
import { uploadBase64ImageToBlob } from '@/lib/utils'
import { PoolClient } from 'pg'
import { v4 as uuidv4 } from 'uuid'
import { sendOrderConfirmation } from './email'

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image: string
  customDesignId?: string
  customDesignData?: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

export async function createOrder({
  userId,
  items,
  deliveryMethod,
  deliveryAddress,
  pickupLocation,
  specialInstructions,
  subtotal,
  deliveryFee,
  promotionalDiscount,
  totalAmount,
}: {
  userId: string
  items: OrderItem[]
  deliveryMethod: 'PICKUP' | 'DELIVERY'
  deliveryAddress?: DeliveryAddress
  pickupLocation?: string
  specialInstructions?: string
  subtotal: number
  deliveryFee: number
  promotionalDiscount: number
  totalAmount: number
}) {
  const pool = getPool()
  const client = await pool.connect()
  
  let customDesignImageUrl = null;

  try {
    await client.query('BEGIN')
    const user = await getUserInfo(client, userId);
    const actualUserId = user.id;

    // Upload custom design image to Vercel Blob if it exists
    if (items[0]?.customDesignData) {
      try {
        const fileName = `custom-designs/${uuidv4()}.png`;
        customDesignImageUrl = await uploadBase64ImageToBlob(items[0].customDesignData, fileName);
        console.log("Successfully uploaded custom design to Vercel Blob:", customDesignImageUrl);
      } catch (error) {
        console.error("Failed to upload custom design to Vercel Blob:", error);
        // Continue with order creation even if image upload fails
      }
    }

    const getNextOrderNumber = async (client: PoolClient) => {
      // Use a more efficient approach with sequence or atomic increment
      const result = await client.query(
        `SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 5) AS INTEGER)), 0) + 1 as next_number
         FROM orders
         WHERE order_number ~ '^ORD-[0-9]+$'`
      )
      const nextNumber = result.rows[0].next_number
      return `ORD-${nextNumber.toString().padStart(6, '0')}`
    }

    const orderNumber = await getNextOrderNumber(client)
    const orderResult = await client.query(
      `INSERT INTO orders (
        user_id, order_number, total_amount, delivery_method,
        delivery_fee, promotional_discount, special_instructions, custom_design_image_url, pickup_location
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id`,
      [
        actualUserId,
        orderNumber,
        totalAmount,
        deliveryMethod,
        deliveryFee,
        promotionalDiscount,
        specialInstructions || null,
        customDesignImageUrl,
        deliveryMethod === 'PICKUP' ? pickupLocation : null
      ]
    )

    const orderId = orderResult.rows[0].id

    // Batch insert order items for better performance
    if (items.length > 0) {
      const orderItemsValues = items.map((item) => [
        orderId,
        item.id,
        Array.isArray(item.name) ? item.name[0] : item.name,
        item.quantity,
        item.price,
        item.price * item.quantity
      ]);

      const placeholders = orderItemsValues.map((_, index) =>
        `($${index * 6 + 1}, $${index * 6 + 2}, $${index * 6 + 3}, $${index * 6 + 4}, $${index * 6 + 5}, $${index * 6 + 6})`
      ).join(', ');

      const flatValues = orderItemsValues.flat();

      await client.query(
        `INSERT INTO order_items (
          order_id, product_id, product_name,
          quantity, price_at_order, item_subtotal
        ) VALUES ${placeholders}`,
        flatValues
      );
    }

    if (deliveryMethod === 'DELIVERY' && deliveryAddress) {
      await client.query(
        `INSERT INTO delivery_addresses (
          order_id, street, city, province, postal_code
        ) VALUES ($1, $2, $3, $4, $5)`,
        [
          orderId,
          deliveryAddress.street,
          deliveryAddress.city,
          deliveryAddress.province,
          deliveryAddress.postalCode
        ]
      )
    }

    await client.query('COMMIT')

    // Get estimated delivery time based on delivery method
    const currentDate = new Date()
    const estimatedDeliveryTime = deliveryMethod === 'DELIVERY'
      ? new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toLocaleDateString() // 2 days for delivery
      : '30 minutes for pickup'

    try {
      console.log("Sending order confirmation email with custom design URL:", customDesignImageUrl);
      await sendOrderConfirmation({
        orderNumber,
        items,
        subtotalAmount: subtotal,
        promotionalDiscount,
        hasPromotionalItems: promotionalDiscount > 0,
        deliveryFee,
        totalAmount,
        deliveryMethod,
        deliveryAddress,
        pickupLocation: deliveryMethod === 'PICKUP' ? pickupLocation : undefined,
        estimatedDeliveryTime,
        customerName: user.name || 'Valued Customer',
        customerEmail: user.email,
        customerPhone: user.phone || '',
        specialInstructions,
        customDesignImageUrl
      })
      console.log('Order confirmation email sent successfully')
      
    } catch (emailError) {
      console.error('Error sending emails:', emailError)
    }

    return {
      success: true,
      orderId: orderId.toString(),
      orderNumber
    }
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('Error creating order:', error)
    throw new Error('Failed to create order')
  } finally {
    client.release()
  }
} 