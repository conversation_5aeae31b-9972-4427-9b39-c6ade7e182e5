'use server'

import { saltAndHashPassword } from "@/lib/auth-utils"
import { Pool } from "@neondatabase/serverless"
import { v4 as uuidv4 } from "uuid"

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
})

export async function register(
  email: string,
  password: string,
  name: string
): Promise<{ success: boolean; userId?: string; error?: string }> {
  try {
    const client = await pool.connect()

    try {
      await client.query("BEGIN")

      const userId = uuidv4()
      const hashedPassword = await saltAndHashPassword(password)

      await client.query(
        `INSERT INTO users (id, email, password, name)
         VALUES ($1, $2, $3, $4)`,
        [userId, email, hashedPassword, name]
      )

      await client.query("COMMIT")

      return {
        success: true,
        userId
      }
    } catch (error) {
      await client.query("ROLLBACK")
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error("Registration error:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to register user"
    }
  }
} 