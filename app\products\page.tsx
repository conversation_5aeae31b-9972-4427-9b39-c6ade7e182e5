"use client"

import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useCartStore } from "@/store/cart-store"
import { toast } from "sonner"

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  slug: string
  express: boolean
}

const products: Product[] = [
  {
    id: "1",
    name: "Business Cards",
    description: "Premium quality business cards with various finishes",
    price: 49.99,
    image: "/images/premium.jpg",
    slug: "business-cards",
    express: true
  },
  {
    id: "2",
    name: "Flyers & Brochures",
    description: "High-quality flyers and brochures for your marketing needs",
    price: 99.99,
    image: "/images/sign.jpg",
    slug: "flyers",
    express: true
  },
  {
    id: "3",
    name: "Posters & Signs",
    description: "Large format printing for posters and signs",
    price: 149.99,
    image: "/images/board.jpg",
    slug: "posters",
    express: false
  },
  {
    id: "4",
    name: "Banners & Displays",
    description: "Professional banners and display materials",
    price: 199.99,
    image: "/images/busines-card.png",
    slug: "banners",
    express: false
  }
]

export default function ProductsPage() {
  const { addItem } = useCartStore()

  const handleAddToCart = (product: Product) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    })
    toast("Added to cart", {
      description: `${product.name} has been added to your cart.`
    })
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Our Products</h1>
        <div className="flex gap-4">
          <Button variant="outline">Filter</Button>
          <Button variant="outline">Sort</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <Card key={product.id} className="overflow-hidden group">
            <div className="relative aspect-square">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
              {product.express && (
                <span className="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                  Express
                </span>
              )}
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-1">{product.name}</h3>
              <p className="text-gray-500 text-sm mb-2">{product.description}</p>
              <div className="flex justify-between items-center">
                <p className="font-bold text-blue-600">${product.price.toFixed(2)} CAD</p>
                <Button 
                  onClick={() => handleAddToCart(product)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Add to Cart
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
} 