@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Define custom theme variables */
:root {
  --background: #ffffff;
  --foreground: #0f172a;

  --card: #ffffff;
  --card-foreground: #0f172a;

  --popover: #ffffff;
  --popover-foreground: #0f172a;

  --primary: #0078d4;
  --primary-foreground: #f8fafc;

  --secondary: #f1f5f9;
  --secondary-foreground: #1e293b;

  --muted: #f1f5f9;
  --muted-foreground: #64748b;

  --accent: #f1f5f9;
  --accent-foreground: #1e293b;

  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;

  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #0078d4;

  --radius: 0.5rem;
}

.dark {
  --background: #0f172a;
  --foreground: #f8fafc;

  --card: #0f172a;
  --card-foreground: #f8fafc;

  --popover: #0f172a;
  --popover-foreground: #f8fafc;

  --primary: #0078d4;
  --primary-foreground: #1e293b;

  --secondary: #334155;
  --secondary-foreground: #f8fafc;

  --muted: #334155;
  --muted-foreground: #94a3b8;

  --accent: #334155;
  --accent-foreground: #f8fafc;

  --destructive: #7f1d1d;
  --destructive-foreground: #f8fafc;

  --border: #334155;
  --input: #334155;
  --ring: #0078d4;
}

/* Base styles */
@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  /* Hide only vertical scrollbar while keeping horizontal scrollbar */

  /* For WebKit browsers (Chrome, Safari, newer versions of Opera) */

  /* For Firefox */

  /* Re-enable horizontal scrollbar for Firefox */
  /* This is a workaround as Firefox doesn't allow separate styling */
  /* You may need a JavaScript solution for perfect control in Firefox */

  /* For Internet Explorer and Edge */
  * {
    -ms-overflow-style: none; /* IE and Edge - hides both scrollbars */
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

